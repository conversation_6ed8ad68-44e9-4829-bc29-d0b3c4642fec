import pandas as pd
import numpy as np
from model_script import *
from utilts import *
from tqdm import tqdm 

filled_df=pd.read_csv("筛选后协变量特征值列表.csv")
print(filled_df.shape)
# 按location分组
grouped = filled_df.groupby('location')
# 创建字典，key是location，value是对应的分组数据框
result_dict = {location: group for location, group in grouped}
# 打印结果
for location, group in result_dict.items():
    print(f"Location: {location}")
    print(group)
    break

# 基本结构查看
print("数据类型:", type(result_dict))
print("国家数量:", len(result_dict))
print("\n所有键(国家):", list(result_dict.keys()))
# 查看详细内容
print("\n完整数据内容:")
j=0
for country, data in result_dict.items():
    print(f"\n国家: {country}")
    print(f"数据: {data.iloc[:10,:10]}")
    j+=1
    if j>=5:
        break

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """
    将 result_dict 中所有国家的特征数据标准化到 [0, 1] 范围，并在标准化前检测异常值
    :param result_dict: 输入的原始数据字典
    :param outlier_threshold: IQR 倍数，用于定义异常值阈值（默认 1.5）
    :param handle_outliers: 处理异常值的方式，'cap'（限制到边界）、'remove'（移除）、'keep'（保留）
    :return: 标准化后的新字典 result_dict_scale
    """
    result_dict_scale = {}
    scaler_dict = {} 
    
    for country, df in result_dict.items():
        scaler = MinMaxScaler()
        features = df.iloc[:, 2:].copy()
        
        # 检测和处理异常值
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                # 计算四分位距 (IQR)
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                # 检测异常值
                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                # 处理异常值
                if handle_outliers == 'cap':
                    # 限制到边界值
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    # 移除包含异常值的行（注意：这会影响整个 DataFrame）
                    features = features[~outliers]
                    # 如果移除了行，同步更新 location 和 year
                    df = df.loc[features.index]

        # 标准化到 [0, 1]
        scaled_features = scaler.fit_transform(features)
        
        # ==================== 修改 2：保存这个 scaler ====================
        scaler_dict[country] = scaler

        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    # ==================== 修改 3：返回两个结果 ====================
    return result_dict_scale, scaler_dict

result_dict_scale, scaler_dict_global = scale_result_dict(result_dict, outlier_threshold=10, handle_outliers='cap')

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv
from torch_geometric.data import Data
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import pandas as pd
import random
from sklearn.decomposition import PCA
from torch_geometric.nn import GATConv 

def set_seed(seed):
    """
    设置随机种子以确保结果可复现
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # if you are using multi-GPU.
        # 确保 cudnn 的确定性，可能会牺牲一些性能
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

def preprocess_data(result_dict, years=range(1960, 2024), exclude_cols=['location', 'year'], n_components=128):
    """
    预处理数据，包括展平、全局标准化和PCA降维。
    
    :param result_dict: 输入的数据字典
    :param years: 年份范围
    :param exclude_cols: 要排除的列
    :param n_components: PCA降维后的目标维度
    :return: 降维后的特征张量，国家列表，原始特征列名
    """
    countries = list(result_dict.keys())
    sample_df = result_dict[countries[0]]
    feature_cols = [col for col in sample_df.columns if col not in exclude_cols]
    num_features = len(feature_cols) * len(years)
    
    # 步骤 1: 展平时间序列数据
    print("Flattening time-series data...")
    features = np.zeros((len(countries), num_features))
    for i, country in enumerate(countries):
        df = result_dict[country]
        for j, year in enumerate(years):
            for k, col in enumerate(feature_cols):
                value = df[df['year'] == float(year)][col].values
                features[i, j * len(feature_cols) + k] = value[0] if len(value) > 0 else 0
    
    # 步骤 2: 全局 Z-score 标准化 (这是PCA所需要的)
    print("Applying global Z-score standardization...")
    features_scaled = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
    
    # <--- PCA 集成开始 --->
    print(f"Applying PCA to reduce dimensions to {n_components}...")
    pca = PCA(n_components=n_components)
    features_reduced = pca.fit_transform(features_scaled)
    
    # 打印PCA解释的方差比例，这有助于判断n_components是否合适
    explained_variance = pca.explained_variance_ratio_.sum()
    print(f"PCA complete. Explained variance ratio by {n_components} components: {explained_variance:.4f}")
    # <--- PCA 集成结束 --->

    # 步骤 3: 转换为 PyTorch 张量
    return torch.tensor(features_reduced, dtype=torch.float), countries, feature_cols

def build_graph(features, k=5):
    sim_matrix = cosine_similarity(features.numpy())
    edge_index = []
    for i in range(len(sim_matrix)):
        neighbors = np.argsort(sim_matrix[i])[::-1][1:k+1]
        for j in neighbors:
            edge_index.append([i, j])
            edge_index.append([j, i])
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    return edge_index

class GCNModel(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels):
        super(GCNModel, self).__init__()
        self.conv1 = GCNConv(in_channels, hidden_channels)
        self.conv2 = GCNConv(hidden_channels, out_channels)
    
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)
        x = self.conv2(x, edge_index)
        return x

# 优化后的实现
# 优化和修正后的训练函数
def train_gcn(data, model, epochs=1000, lr=0.005, temperature=0.1, neg_samples=100):
    """
    使用修正且高效的 InfoNCE Loss 训练 GCN 模型
    """
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
    model.train()
    
    num_nodes = data.x.size(0)
    
    print(f"Starting training with corrected InfoNCE Loss...")
    print(f"LR={lr}, Temp={temperature}, NegSamples={neg_samples}, Epochs={epochs}")
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        
        # 1. 获取所有节点的 embedding
        embeddings = model(data)
        
        # 2. 提取所有的正样本对 (anchor, positive)
        # edge_index 的形状是 [2, num_edges]
        # anchors 是所有边的起点，positives 是所有边的终点
        anchors = embeddings[data.edge_index[0]]
        positives = embeddings[data.edge_index[1]]
        
        # 3. 为每个 anchor 高效地采样负样本
        # 我们一次性为所有边中的 anchor 采样负样本
        num_edges = data.edge_index.size(1)
        
        # 生成负样本索引 [num_edges, neg_samples]
        neg_indices = torch.randint(0, num_nodes, (num_edges, neg_samples), device=embeddings.device)
        
        # 从 embedding 矩阵中取出所有负样本
        # a. 扩展 neg_indices 以便高级索引: [num_edges * neg_samples]
        # b. 取出 embeddings: [num_edges * neg_samples, out_channels]
        # c. 重塑为: [num_edges, neg_samples, out_channels]
        negative_embeddings = embeddings[neg_indices.view(-1)].view(num_edges, neg_samples, -1)

        # 4. 计算相似度
        # 正样本相似度: [num_edges]
        # bmm 是批量矩阵乘法, 我们需要调整维度
        # anchors: [num_edges, 1, out_channels]
        # positives: [num_edges, out_channels, 1]
        pos_sim = F.cosine_similarity(anchors, positives) / temperature

        # 负样本相似度: [num_edges, neg_samples]
        # anchors: [num_edges, 1, out_channels]
        # negative_embeddings: [num_edges, out_channels, neg_samples] (需要转置)
        neg_sim = F.cosine_similarity(anchors.unsqueeze(1), negative_embeddings, dim=-1) / temperature
        
        # 5. 计算 InfoNCE Loss
        # pos_sim_exp: [num_edges]
        pos_sim_exp = torch.exp(pos_sim)
        
        # neg_sim_exp_sum: [num_edges]
        neg_sim_exp_sum = torch.exp(neg_sim).sum(dim=1)
        
        # 对于每个正样本对，分母是其自身与所有负样本
        denominator = pos_sim_exp + neg_sim_exp_sum
        
        # 计算 loss，并取平均
        loss = -torch.log(pos_sim_exp / denominator).mean()
        
        loss.backward()
        optimizer.step()
        
        if epoch % 50 == 0:
            print(f"Epoch {epoch:04d}, Loss: {loss.item():.6f}")
            
    # 注意：train_gcn 函数现在只返回最终的 loss， embeddings 在训练后单独获取
    model.eval()
    with torch.no_grad():
        final_embeddings = model(data)
    return final_embeddings

def get_embeddings(model, data):
    model.eval()
    with torch.no_grad():
        embeddings = model(data)
    return embeddings

def main(result_dict):
    set_seed(2025)
    
    # --- 1. 数据预处理 ---
    # 建议：直接使用原始的 result_dict，跳过 scale_result_dict
    # Z-score 标准化对于 PCA 和 GCN 的输入来说是更标准的做法
    print("Step 1: Preprocessing data with PCA...")
    PCA_COMPONENTS = 128
    features, countries, feature_cols = preprocess_data(result_dict, # <-- 使用原始 result_dict
                                                      years=range(1960, 2019), 
                                                      n_components=PCA_COMPONENTS)
    in_channels = features.shape[1]
    print(f"Features shape after PCA: {features.shape}")

    # --- 2. 图构建 ---
    k_neighbors = 10 # 可以适当增加 K 值
    print("Step 2: Building graph with k-nearest neighbors...")
    edge_index = build_graph(features, k=k_neighbors)
    data = Data(x=features, edge_index=edge_index)
    print(f"Number of edges: {edge_index.size(1)}")

    # --- 3. 模型初始化 ---
    hidden_channels = 256
    out_channels = 64
    print("Step 3: Initializing GCN model...")
    model = GCNModel(in_channels=in_channels, hidden_channels=hidden_channels, out_channels=out_channels)

    # --- 4. 训练模型 (使用修正的参数和函数) ---
    print("Step 4: Training GCN model with corrected InfoNCE loss...")
    
    # ==================== 关键超参数修改 ====================
    EPOCHS = 1000
    LEARNING_RATE = 0.005   # <-- 大幅降低学习率
    TEMPERATURE = 0.1       # <-- 可以从 0.1 开始尝试
    NEG_SAMPLES = 100       # <-- 保持或根据需要调整
    
    embeddings = train_gcn(data, model, 
                           epochs=EPOCHS, 
                           lr=LEARNING_RATE, 
                           temperature=TEMPERATURE, 
                           neg_samples=NEG_SAMPLES)
    
    # --- 5. 获取并检查 Embeddings ---
    print("\nStep 5: Training finished. Final embeddings generated.")
    print(f"Embeddings shape: {embeddings.shape}")
    
    embeddings_np = embeddings.numpy()
    print("Embedding std (per dimension):", embeddings_np.std(axis=0).mean())
    norms = np.linalg.norm(embeddings_np, axis=1)
    print("Embedding norms (first 5):", norms[:5])
    
    embedding_dict = {country: embeddings[i].numpy() for i, country in enumerate(countries)}
    print("\nSample embeddings:")
    for country in countries[:3]:
        print(f"{country}: {embedding_dict[country][:5]}...")
        
    return embedding_dict

# ==================== 调用流程修改 ====================
# 1. 加载数据
filled_df = pd.read_csv("筛选后协变量特征值列表.csv")
grouped = filled_df.groupby('location')
result_dict = {location: group for location, group in grouped}


# 3. 直接将原始的 result_dict 传入 main 函数

embedding_dict = main(result_dict)       # <-- 新的调用

import torch
import torch.nn.functional as F
# GATConv is already imported, which is great
from torch_geometric.nn import GCNConv, GATConv
from torch_geometric.data import Data
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import pandas as pd
import random
from sklearn.decomposition import PCA

def set_seed(seed):
    """
    设置随机种子以确保结果可复现
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # if you are using multi-GPU.
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

def preprocess_data(result_dict, years=range(1960, 2024), exclude_cols=['location', 'year'], n_components=128):
    """
    预处理数据，包括展平、全局标准化和PCA降维。
    """
    countries = list(result_dict.keys())
    sample_df = result_dict[countries[0]]
    feature_cols = [col for col in sample_df.columns if col not in exclude_cols]
    num_features = len(feature_cols) * len(years)
    
    print("Flattening time-series data...")
    features = np.zeros((len(countries), num_features))
    for i, country in enumerate(countries):
        df = result_dict[country]
        for j, year in enumerate(years):
            for k, col in enumerate(feature_cols):
                value = df[df['year'] == float(year)][col].values
                features[i, j * len(feature_cols) + k] = value[0] if len(value) > 0 else 0
    
    print("Applying global Z-score standardization...")
    features_scaled = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
    
    print(f"Applying PCA to reduce dimensions to {n_components}...")
    pca = PCA(n_components=n_components)
    features_reduced = pca.fit_transform(features_scaled)
    
    explained_variance = pca.explained_variance_ratio_.sum()
    print(f"PCA complete. Explained variance ratio by {n_components} components: {explained_variance:.4f}")
    
    return torch.tensor(features_reduced, dtype=torch.float), countries, feature_cols

def build_graph(features, k=5):
    sim_matrix = cosine_similarity(features.numpy())
    edge_index = []
    for i in range(len(sim_matrix)):
        neighbors = np.argsort(sim_matrix[i])[::-1][1:k+1]
        for j in neighbors:
            edge_index.append([i, j])
            edge_index.append([j, i])
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    return edge_index

# ==================== MODIFICATION 1: Replace GCNModel with GATModel ====================
class GATModel(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels, heads=4):
        """
        Initializes the Graph Attention Network (GAT) model.
        Args:
            in_channels (int): Number of input features.
            hidden_channels (int): Number of features in the hidden layer for each head.
            out_channels (int): Number of output features (embedding dimension).
            heads (int): Number of attention heads.
        """
        super(GATModel, self).__init__()
        # Dropout is often applied within the GATConv layer itself.
        self.conv1 = GATConv(in_channels, hidden_channels, heads=heads, dropout=0.6)
        
        # The second layer takes the concatenated output of all heads from the first layer.
        # Its input dimension is hidden_channels * heads.
        # We use a single head in the final layer for the final embedding.
        self.conv2 = GATConv(hidden_channels * heads, out_channels, heads=1, concat=False, dropout=0.6)

    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        # GAT models typically use ELU activation.
        x = F.elu(self.conv1(x, edge_index))
        # The dropout is already applied inside the GATConv layers.
        x = self.conv2(x, edge_index)
        
        return x

# ==================== MODIFICATION 2: Rename training function ====================
def train_model(data, model, epochs=1000, lr=0.005, temperature=0.1, neg_samples=100):
    """
    使用修正且高效的 InfoNCE Loss 训练图神经网络模型 (GCN, GAT, etc.)
    """
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
    model.train()
    
    num_nodes = data.x.size(0)
    
    print(f"Starting training for {model.__class__.__name__} with corrected InfoNCE Loss...")
    print(f"LR={lr}, Temp={temperature}, NegSamples={neg_samples}, Epochs={epochs}")
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        embeddings = model(data)
        
        anchors = embeddings[data.edge_index[0]]
        positives = embeddings[data.edge_index[1]]
        
        num_edges = data.edge_index.size(1)
        neg_indices = torch.randint(0, num_nodes, (num_edges, neg_samples), device=embeddings.device)
        negative_embeddings = embeddings[neg_indices.view(-1)].view(num_edges, neg_samples, -1)

        pos_sim = F.cosine_similarity(anchors, positives) / temperature
        neg_sim = F.cosine_similarity(anchors.unsqueeze(1), negative_embeddings, dim=-1) / temperature
        
        pos_sim_exp = torch.exp(pos_sim)
        neg_sim_exp_sum = torch.exp(neg_sim).sum(dim=1)
        
        denominator = pos_sim_exp + neg_sim_exp_sum
        loss = -torch.log(pos_sim_exp / denominator).mean()
        
        loss.backward()
        optimizer.step()
        
        if epoch % 50 == 0:
            print(f"Epoch {epoch:04d}, Loss: {loss.item():.6f}")
            
    model.eval()
    with torch.no_grad():
        final_embeddings = model(data)
    return final_embeddings

def main(result_dict):
    set_seed(2025)
    
    # --- 1. 数据预处理 ---
    print("Step 1: Preprocessing data with PCA...")
    PCA_COMPONENTS = 128
    features, countries, feature_cols = preprocess_data(result_dict,
                                                      years=range(1960, 2019), 
                                                      n_components=PCA_COMPONENTS)
    in_channels = features.shape[1]
    print(f"Features shape after PCA: {features.shape}")

    # --- 2. 图构建 ---
    k_neighbors = 10
    print("Step 2: Building graph with k-nearest neighbors...")
    edge_index = build_graph(features, k=k_neighbors)
    data = Data(x=features, edge_index=edge_index)
    print(f"Number of edges: {edge_index.size(1)}")

    # --- 3. 模型初始化 (MODIFICATION 3: Initialize GATModel) ---
    print("Step 3: Initializing GAT model...")
    # GAT hyperparameters
    HEADS = 4  # Number of attention heads
    # To keep model size similar, hidden_channels can be smaller, as total dim = hidden_channels * heads
    HIDDEN_CHANNELS_GAT = 64 
    OUT_CHANNELS = 64 # Final embedding size

    model = GATModel(
        in_channels=in_channels, 
        hidden_channels=HIDDEN_CHANNELS_GAT, 
        out_channels=OUT_CHANNELS, 
        heads=HEADS
    )

    # --- 4. 训练模型 (MODIFICATION 4: Call the renamed function) ---
    # Training hyperparameters
    EPOCHS = 3000
    LEARNING_RATE = 0.001
    TEMPERATURE = 0.07
    NEG_SAMPLES = 100
    
    embeddings = train_model(data, model,  # <-- Using the generic `train_model`
                           epochs=EPOCHS, 
                           lr=LEARNING_RATE, 
                           temperature=TEMPERATURE, 
                           neg_samples=NEG_SAMPLES)
    
    # --- 5. 获取并检查 Embeddings ---
    print("\nStep 5: Training finished. Final embeddings generated.")
    print(f"Embeddings shape: {embeddings.shape}")
    
    embeddings_np = embeddings.numpy()
    print("Embedding std (per dimension):", embeddings_np.std(axis=0).mean())
    norms = np.linalg.norm(embeddings_np, axis=1)
    print("Embedding norms (first 5):", norms[:5])
    
    embedding_dict = {country: embeddings[i].numpy() for i, country in enumerate(countries)}
    print("\nSample embeddings:")
    for country in countries[:3]:
        print(f"{country}: {embedding_dict[country][:5]}...")
        
    return embedding_dict

# ==================== 调用流程 (无变化) ====================
# 1. 加载数据
filled_df = pd.read_csv("筛选后协变量特征值列表.csv")
grouped = filled_df.groupby('location')
result_dict = {location: group for location, group in grouped}

# 2. 直接将原始的 result_dict 传入 main 函数
embedding_dict = main(result_dict)

cor_space_country=pd.DataFrame.from_dict(embedding_dict)
cor_space_country=cor_space_country.T
cor_space_country.to_csv("所有国家空间协变量emb2018.csv")

cor_space_country.head()

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt

def compute_similarity_matrix(embedding_dict):
    countries = list(embedding_dict.keys())
    embeddings = np.array([embedding_dict[country] for country in countries])
    sim_matrix = cosine_similarity(embeddings)
    return sim_matrix, countries

def find_similar_pairs(sim_matrix, countries, top_n=10, min_regions=4):
    num_countries = len(countries)
    sim_pairs = []
    region_dict = {
        'Africa': ['Burkina Faso', 'Algeria', 'Kenya', 'Nigeria'],
        'Europe': ['Poland', 'Albania', 'Germany', 'France'],
        'Americas': ['Grenada', 'Colombia', 'Canada', 'United States of America'],
        'Asia': ['Afghanistan', 'China', 'India', 'Japan'],
        'Oceania': ['Australia', 'New Zealand']
    }
    country_to_region = {}
    for region, region_countries in region_dict.items():
        for country in region_countries:
            if country in countries:
                country_to_region[country] = region
    for i in range(num_countries):
        for j in range(i + 1, num_countries):
            sim_pairs.append((countries[i], countries[j], sim_matrix[i, j]))
    sim_pairs = sorted(sim_pairs, key=lambda x: x[2], reverse=True)
    selected_pairs = []
    covered_regions = set()
    for pair in sim_pairs:
        c1, c2, sim = pair
        r1 = country_to_region.get(c1, 'Other')
        r2 = country_to_region.get(c2, 'Other')
        if (len(covered_regions) < min_regions or sim > 0.99) and len(selected_pairs) < top_n:
            selected_pairs.append(pair)
            covered_regions.add(r1)
            covered_regions.add(r2)
        if len(selected_pairs) >= top_n and len(covered_regions) >= min_regions:
            break
    return selected_pairs

def visualize_embeddings(embedding_dict, sim_pairs):
    countries = list(embedding_dict.keys())
    embeddings = np.array([embedding_dict[country] for country in countries])
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    embeddings_2d = tsne.fit_transform(embeddings)
    plt.figure(figsize=(24, 20))
    plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1], c='lightgray', s=30, alpha=0.5, label='Countries')
    colors = plt.cm.tab10(np.linspace(0, 1, len(sim_pairs)))
    for idx, (c1, c2, sim) in enumerate(sim_pairs):
        i1 = countries.index(c1)
        i2 = countries.index(c2)
        plt.scatter(embeddings_2d[i1, 0], embeddings_2d[i1, 1], c=[colors[idx]], s=100, label=f'{c1} (sim={sim:.3f})')
        plt.scatter(embeddings_2d[i2, 0], embeddings_2d[i2, 1], c=[colors[idx]], s=100)
        plt.plot([embeddings_2d[i1, 0], embeddings_2d[i2, 0]], 
                 [embeddings_2d[i1, 1], embeddings_2d[i2, 1]], 
                 c=colors[idx], linestyle='--', alpha=0.7)
    for i, country in enumerate(countries):
        plt.annotate(country, 
                     (embeddings_2d[i, 0], embeddings_2d[i, 1]), 
                     fontsize=8, 
                     ha='right', 
                     va='bottom', 
                     alpha=0.7, 
                     color='black',
                     xytext=(5, 5), 
                     textcoords='offset points')
    plt.title("t-SNE Visualization of Country Embeddings (All Countries Labeled)", fontsize=16)
    plt.xlabel("t-SNE Component 1", fontsize=12)
    plt.ylabel("t-SNE Component 2", fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    plt.tight_layout()
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.show()

def visualize_embedding_results(embedding_dict, top_n=10):
    print("Step 1: Computing similarity matrix...")
    sim_matrix, countries = compute_similarity_matrix(embedding_dict)
    print(f"Step 2: Finding top {top_n} similar country pairs with diverse regions...")
    top_sim_pairs = find_similar_pairs(sim_matrix, countries, top_n=top_n, min_regions=4)
    print(f"\nTop {top_n} similar country pairs:")
    for c1, c2, sim in top_sim_pairs:
        print(f"{c1} - {c2}: Cosine Similarity = {sim:.4f}")
    print("Step 3: Visualizing embeddings with t-SNE...")
    visualize_embeddings(embedding_dict, top_sim_pairs)

if __name__ == "__main__":
    visualize_embedding_results(embedding_dict, top_n=10)

# --- 提取并保存t-SNE坐标 ---

# 1. 准备数据 (请确保 embedding_dict 这个变量已经创建并包含了GCN的结果)
countries = list(embedding_dict.keys())
embeddings = np.array([embedding_dict[country] for country in countries])

# 2. 进行 t-SNE 计算以获取坐标
#    确保这里的参数与你 visualize_embeddings 函数中的完全一致，以保证结果相同
print("正在进行t-SNE降维计算...")
tsne = TSNE(n_components=2, random_state=42, perplexity=30)
embeddings_2d = tsne.fit_transform(embeddings)
print("计算完成。")

# 3. 创建一个包含国家名和对应坐标的 DataFrame
tsne_df = pd.DataFrame({
    'Country': countries,
    'TSNE_1': embeddings_2d[:, 0],  # X 轴坐标
    'TSNE_2': embeddings_2d[:, 1]   # Y 轴坐标
})

# 4. 将结果保存为 CSV 文件
output_filename = "tsne图结果坐标系.csv"
tsne_df.to_csv(output_filename, index=False)

print(f"t-SNE坐标已成功保存到 '{output_filename}'")
print("\n文件内容预览:")
print(tsne_df.head())

####DBSCAN聚类算法
import pandas as pd
import numpy as np
from sklearn.manifold import TSNE
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# --- 步骤 1: 准备数据 ---
# 请确保在你运行这个单元格之前，`embedding_dict` 变量已经包含了GCN模型的结果。
print("正在准备数据...")
countries = list(embedding_dict.keys())
embeddings_32d = np.array([embedding_dict[country] for country in countries])

# --- 步骤 2: 进行 t-SNE 降维计算 ---
# 这里的参数与你之前的可视化代码完全一致，以保证结果相同。
print("正在进行t-SNE降维计算...")
tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=1000)
embeddings_2d = tsne.fit_transform(embeddings_32d)
print("t-SNE计算完成。")

# --- 步骤 3: 使用DBSCAN进行聚类 ---
# 在使用DBSCAN之前，对数据进行标准化是一个好习惯，可以使eps参数更直观。
embeddings_2d_scaled = StandardScaler().fit_transform(embeddings_2d)

# 初始化并运行DBSCAN算法。
# eps: 定义了邻域的半径。值越大，越容易形成大的簇。
# min_samples: 定义了形成一个簇所需要的最少样本数。
print("正在进行DBSCAN聚类...")
dbscan = DBSCAN(eps=0.26, min_samples=4)
cluster_labels = dbscan.fit_predict(embeddings_2d_scaled)
print("聚类完成。")

# --- 步骤 4: 创建并保存最终结果 ---
# 将国家名、t-SNE坐标和聚类标签（组号）合并到一个DataFrame中。
# 聚类标签为 -1 代表该点是离群点（噪声），不属于任何一个簇。
result_df = pd.DataFrame({
    'Country': countries,
    'TSNE_1': embeddings_2d[:, 0],  # X 轴坐标
    'TSNE_2': embeddings_2d[:, 1],  # Y 轴坐标
    'Cluster': cluster_labels       # 聚类组号
})

# 将结果保存为CSV文件
output_filename = "country_clusters.csv"
result_df.to_csv(output_filename, index=False)

# --- 步骤 5: 打印汇总信息和结果预览 ---
n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
n_noise = list(cluster_labels).count(-1)

print("\n--- 聚类结果汇总 ---")
print(f"发现的簇（组）数量: {n_clusters}")
print(f"被识别为噪声点的国家数量: {n_noise}")

print(f"\n聚类结果已成功保存到 '{output_filename}'")
print("\n文件内容预览:")
print(result_df.head())

# (可选) 绘制一个简单的、按簇着色的图来预览效果
plt.figure(figsize=(12, 10))
unique_labels = set(cluster_labels)
colors = [plt.cm.Spectral(each) for each in np.linspace(0, 1, len(unique_labels))]

for k, col in zip(unique_labels, colors):
    if k == -1:
        # 将噪声点用黑色表示
        col = [0, 0, 0, 1]

    class_member_mask = (cluster_labels == k)
    
    xy = embeddings_2d[class_member_mask]
    plt.plot(xy[:, 0], xy[:, 1], 'o', markerfacecolor=tuple(col),
             markeredgecolor='k', markersize=8, label=f'Cluster {k}')

plt.title(f'DBSCAN聚类结果 (共 {n_clusters} 个簇)')
plt.legend()
plt.grid(True)
plt.show()



from model_script import *
from utilts import *
from tqdm import tqdm 
import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import gc


filled_df=pd.read_csv("筛选后协变量特征值列表.csv")
print(filled_df.shape)
# 按location分组
grouped = filled_df.groupby('location')
# 创建字典，key是location，value是对应的分组数据框
result_dict = {location: group for location, group in grouped}
# 打印结果
for location, group in result_dict.items():
    print(f"Location: {location}")
    print(group)
    break

def split_train_test_years(df, test_size=-5):
    """划分训练和测试年份"""
    years = df['year'].sort_values().unique()
    if abs(test_size) >= len(years):
        raise ValueError("Test size must be smaller than the total number of years")
    test_years = years[test_size:].tolist()
    train_years = years[:test_size].tolist()
    return train_years, test_years

def get_train_test_data(df, train_years, test_years):
    """根据年份划分训练和测试数据集"""
    train_df = df[df['year'].isin(train_years)]
    test_df = df[df['year'].isin(test_years)]
    return train_df, test_df

def standardize_data(df, feature_start_idx=2):
    """标准化特征数据"""
    scaler = MinMaxScaler()
    features_scaled = scaler.fit_transform(df.iloc[:, feature_start_idx:])
    return pd.DataFrame(features_scaled, columns=df.iloc[:, feature_start_idx:].columns), scaler


def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数（与最终预测模型一致的版本）"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )

            # --- 使用 RBF + Linear 组合核 ---
            time_kernel = gpytorch.kernels.ScaleKernel(gpytorch.kernels.RBFKernel(active_dims=[0])) + \
                          gpytorch.kernels.ScaleKernel(gpytorch.kernels.LinearKernel(active_dims=[0]))

            embedding_kernel = gpytorch.kernels.ScaleKernel(gpytorch.kernels.RBFKernel(active_dims=range(1, 65)))

            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.ProductKernel(time_kernel, embedding_kernel),
                num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=500, lr=0.01):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)
    loss_history = [] # 新增一个列表来记录损失

    # 【修改这里】为训练迭代也加上tqdm
    # leave=False 表示这个内部进度条完成后会消失，保持界面干净
    for i in tqdm(range(training_iter), desc="模型训练中", leave=False):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        #print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
        loss_history.append(loss.item()) # 记录当前损失
    return model, likelihood, loss_history


def predict_values(model, likelihood, train_len, pred_steps, embedding_vector, device):
    """使用训练好的模型进行预测（修正了时间步生成逻辑）"""
    
    # --- 核心修正：正确地生成连续的未来时间步 ---
    future_time_steps = torch.arange(train_len, train_len + pred_steps, dtype=torch.float32).view(-1, 1).to(device)
    
    # 准备嵌入向量
    embedding_tensor = torch.tensor(embedding_vector, dtype=torch.float32).to(device)
    embedding_repeated = embedding_tensor.repeat(pred_steps, 1)
    
    # 组合成预测输入
    X_pred = torch.cat([future_time_steps, embedding_repeated], dim=1)
    
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        predictions = likelihood(model(X_pred)).mean.cpu().numpy()
    return predictions

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, test_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', test_years)
    return pred_df

def safe_mape(y_true, y_pred):
    """安全地计算MAPE，避免除以零"""
    y_true, y_pred = np.array(y_true), np.array(y_pred)
    # 过滤掉真实值为0的情况
    non_zero_mask = y_true != 0
    if np.sum(non_zero_mask) == 0:
        return np.nan # 如果所有真实值都为0，无法计算MAPE
    return np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask])) * 100

def smape(y_true, y_pred):
    """计算sMAPE"""
    y_true, y_pred = np.array(y_true), np.array(y_pred)
    numerator = np.abs(y_pred - y_true)
    denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
    # 避免分母为0
    non_zero_mask = denominator != 0
    if np.sum(non_zero_mask) == 0:
        return np.nan
    return np.mean(numerator[non_zero_mask] / denominator[non_zero_mask]) * 100

def mase(y_true, y_pred, y_train):
    """计算MASE"""
    y_true, y_pred, y_train = np.array(y_true), np.array(y_pred), np.array(y_train)
    # 计算分子：预测误差的MAE
    mae_forecast = np.mean(np.abs(y_true - y_pred))
    # 计算分母：训练集上朴素预测的MAE
    mae_naive_insample = np.mean(np.abs(y_train[1:] - y_train[:-1]))
    if mae_naive_insample == 0:
        return np.nan # 无法计算
    return mae_forecast / mae_naive_insample

def evaluate_predictions(true_df, pred_df, train_df_raw, feature_start_idx=2):
    """
    计算新的相对评估指标：R², MAPE, sMAPE, MASE
    接收 train_df_raw 用于计算MASE
    """
    true_df = true_df.set_index('year')
    pred_df = pred_df.set_index('year')
    train_df_raw = train_df_raw.set_index('year') # 确保train_df也有年份索引

    common_years = true_df.index.intersection(pred_df.index)
    feature_cols = true_df.columns[feature_start_idx:]
    
    # 初始化指标列表
    r2_list, mape_list, smape_list, mase_list = [], [], [], []

    for year in common_years:
        true_vals = true_df.loc[year, feature_cols].values
        pred_vals = pred_df.loc[year, feature_cols].values
        # MASE需要用到所有训练数据，而不是按年
        train_vals = train_df_raw[feature_cols].values

        # 计算各项指标
        r2 = r2_score(true_vals, pred_vals)
        mape_val = safe_mape(true_vals, pred_vals)
        smape_val = smape(true_vals, pred_vals)
        mase_val = mase(true_vals, pred_vals, train_vals)

        # 添加到列表
        r2_list.append(r2)
        mape_list.append(mape_val)
        smape_list.append(smape_val)
        mase_list.append(mase_val)
    
    return [r2_list, mape_list, smape_list, mase_list]

def process_single_country(df, test_size, device, embedding_df, first_layer_scaler):
    """
    处理单个国家的预测和评估。
    【修改】：接收 first_layer_scaler 参数，用于最终的逆标准化。
    """
    country = df['location'].iloc[0]
    train_years, test_years = split_train_test_years(df, test_size)
    
    # 这里的 train_df 和 test_df 都是第一次标准化后的数据
    train_df, test_df = get_train_test_data(df, train_years, test_years)

    # 对训练数据进行二次标准化，并保存好局部的 scaler (second_scaler)
    features_scaled, second_scaler = standardize_data(train_df)
    
    # 获取国家嵌入向量（32 维）
    embedding_vector = embedding_df.loc[country].values.reshape(1, -1)  # [1, 32]
    
    # 准备训练输入：时间 + 嵌入向量
    time_steps = np.arange(len(features_scaled)).reshape(-1, 1)  # [num_years, 1]
    embedding_repeated = np.repeat(embedding_vector, len(features_scaled), axis=0)  # [num_years, 32]
    X_train = np.hstack([time_steps, embedding_repeated])  # [num_years, 33]
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood, loss_history = train_model(model, likelihood, X_train, y_train)

    # 预测
    pred_steps = len(test_years)
    predictions = predict_values(model, likelihood, len(train_df), pred_steps, embedding_vector, device)

    # 步骤 1: 对预测结果进行第一次逆标准化 (使用 second_scaler)
    # 将其从“二次局部尺度”还原到“一次全局尺度”
    predictions_first_scale = second_scaler.inverse_transform(predictions)
    
    # 步骤 2: 对预测结果进行第二次逆标准化 (使用 first_layer_scaler)
    # 将其从“一次全局尺度”还原到“原始粗数据尺度”
    predictions_raw = first_layer_scaler.inverse_transform(predictions_first_scale)

    # 同时，也要对验证集进行逆标准化，还原到“原始粗数据尺度”
    test_features_raw = first_layer_scaler.inverse_transform(test_df.iloc[:, 2:])
# --- 【新增】将训练集也反标准化，为MASE做准备 ---
    train_features_raw = first_layer_scaler.inverse_transform(train_df.iloc[:, 2:])
    # 清理内存
    cleanup_memory()

    # 使用还原到粗数据尺度的结果来构建 DataFrame
    pred_df_raw = build_prediction_df(predictions_raw, country, test_years, features_scaled.columns)
    test_df_raw = build_prediction_df(test_features_raw, country, test_years, features_scaled.columns)
    
    # --- 【新增】为train_df_raw也构建DataFrame ---
    train_df_raw_full = build_prediction_df(train_features_raw, country, train_years, features_scaled.columns)
    # ----------------------------------------
    
    # --- 【修改】调用新的评估函数 ---
    metrics = evaluate_predictions(test_df_raw, pred_df_raw, train_df_raw_full)
    # --------------------------------
    return metrics, loss_history

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """标准化 result_dict 数据"""
    result_dict_scale = {}
    # 创建一个空字典，用来存放每个国家的scaler
    scaler_dict = {} 

    for country, df in result_dict.items():
        # 为每个国家创建一个新的scaler实例
        scaler = MinMaxScaler()
        features = df.iloc[:, 2:].copy()
        
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                if handle_outliers == 'cap':
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    features = features[~outliers]
                    df = df.loc[features.index]

        # 使用 scaler 对粗数据进行第一次标准化
        scaled_features = scaler.fit_transform(features)
        
        # 将这个为特定国家学习到的 scaler 保存到字典中
        scaler_dict[country] = scaler
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale, scaler_dict

# 在主流程循环之前，获取测试年份
# 假设所有国家的时间范围一致，我们用第一个国家来确定测试年份
test_size = -5 # 首先定义 test_size
any_country_df = result_dict[next(iter(result_dict))] 
_, test_years = split_train_test_years(any_country_df, test_size)


# ==============================================================================
# 主流程 (最终执行版)
# ==============================================================================

# 假设原始的 result_dict 已经加载
import matplotlib.pyplot as plt

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
metric_dict = {}
test_size = -5

# 步骤 1：进行第一次全局标准化，并获取所有国家的 scaler
result_dict_scale, scaler_dict_global = scale_result_dict(result_dict, outlier_threshold=10, handle_outliers='cap')

# 步骤 2：加载GCN生成的国家嵌入数据
cor_space_country = pd.read_csv("所有国家空间协变量emb2018.csv", index_col=[0])

# --- 【请在这里添加】 ---
countries_to_process = list(result_dict_scale.keys())

# 步骤 3：遍历所有国家，执行预测和评估
# tqdm会自动创建进度条，显示进度、耗时和ETA
# 【修正后的版本】

# 步骤 3：遍历所有国家，执行预测和评估
for country in tqdm(countries_to_process, desc="国家总进度"):
    df_target = result_dict_scale[country]
    first_layer_scaler = scaler_dict_global[country]
    
    # 执行完整的单国处理流程
    metrics, loss_history = process_single_country(
        df_target, test_size, device, cor_space_country, first_layer_scaler
    )
    
    # 将计算出的新指标列表存入字典
    metric_dict[country] = metrics
    
        # --- 【新增这里】绘制并保存每个国家的损失曲线图 ---
    plt.figure(figsize=(10, 6))
    plt.plot(loss_history)
    plt.title(f'Loss Curve for {country}')
    plt.xlabel('Training Iteration')
    plt.ylabel('Negative Log Marginal Likelihood')
    plt.grid(True)
    # 创建一个文件夹来保存图片，例如 'loss_curves'
    import os
    if not os.path.exists('loss_curves'):
        os.makedirs('loss_curves')
    plt.savefig(f'loss_curves/loss_{country}.png')
    plt.close() # 关闭图形，防止在屏幕上显示并消耗内存

import pandas as pd
import numpy as np

# 假设 metric_dict 已经从上一个cell正确生成
# 并且 test_years 也已经定义

# 1. 初始数据转换
df = pd.DataFrame.from_dict(metric_dict).T.copy()
# 新的列名
df.columns = ['R2_list', 'MAPE_list', 'sMAPE_list', 'MASE_list']
metrics_names = ['R2', 'MAPE', 'sMAPE', 'MASE']

# --- 创建一个包含所有年度和平均指标的、更详细的DataFrame ---
all_results = []
for country in df.index:
    for i, metric_name in enumerate(metrics_names):
        yearly_values = df.loc[country, f'{metric_name}_list']
        # 计算5年平均值
        mean_val = np.nanmean(yearly_values) # 使用nanmean忽略可能的nan值
        
        # 记录每年的值和5年平均值
        row_data = {'Country': country, 'Metric': metric_name, '5yr_Avg': mean_val}
        for j, year in enumerate(test_years):
            row_data[f'Year_{year}'] = yearly_values[j]
        all_results.append(row_data)

long_format_df = pd.DataFrame(all_results)
print("--- Detailed Results by Country and Metric (Long Format) ---")
print(long_format_df.head(8)) # 打印前两个国家的结果看看


# --- 2. 计算并展示【年度】全球描述性统计 ---
yearly_stats_list = []
for year in test_years:
    year_col = f'Year_{year}'
    # 按指标分组，计算该年份的统计数据
    stats = long_format_df.groupby('Metric')[year_col].agg(['median', 'std', lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]).reset_index()
    stats.columns = ['Metric', 'Median', 'Std_Dev', '25th_Percentile', '75th_Percentile']
    stats.insert(0, 'Year', year)
    yearly_stats_list.append(stats)

yearly_global_stats_df = pd.concat(yearly_stats_list, ignore_index=True)
print("\n\n--- Yearly Global Descriptive Statistics ---")
print(yearly_global_stats_df)


# --- 3. 计算并展示【5年汇总】全球描述性统计 ---
agg_stats = long_format_df.groupby('Metric')['5yr_Avg'].agg(['median', 'std', lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]).reset_index()
agg_stats.columns = ['Metric', 'Median', 'Std_Dev', '25th_Percentile', '75th_Percentile']

print("\n\n--- 5-Year Aggregated Global Descriptive Statistics ---")
print(agg_stats)


# --- 4. 保存结果到CSV ---
# 保存每个国家的详细结果，用于后续分析
long_format_df.to_csv("prediction_metrics_detailed_by_country.csv", index=False)

# 保存两个全局统计结果
yearly_global_stats_df.to_csv("prediction_metrics_yearly_global_stats.csv", index=False)
agg_stats.to_csv("prediction_metrics_5yr_agg_global_stats.csv", index=False)

print("\nAll detailed and summary results have been saved to CSV files.")

import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm
import gc
# 请确保也导入了 LinearKernel
from gpytorch.kernels import MultitaskKernel, RBFKernel, ScaleKernel, ProductKernel, LinearKernel
from sklearn.preprocessing import StandardScaler
def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数（已修改，增加长期趋势预测能力）"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            
            # 均值函数保持不变，继续使用 ConstantMean
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )

            # --- 所有的核函数定义都应该在这里，在 __init__ 内部 ---
            
            # 1. 为时间维度（第0维）创建一个组合核
            time_kernel = ScaleKernel(RBFKernel(active_dims=[0])) + \
                          ScaleKernel(LinearKernel(active_dims=[0]))

            # 2. 为你的32维空间嵌入创建一个RBF核
            embedding_kernel = ScaleKernel(RBFKernel(active_dims=range(1, 65)))

            # 3. 将时间和空间核组合起来
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.ProductKernel(time_kernel, embedding_kernel),
                num_tasks=y_train.shape[1], rank=1
            )
            # --- 缩进修正结束 ---

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=80, lr=0.1):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    # 【修改这里】为训练迭代也加上tqdm
    # leave=False 表示这个内部进度条完成后会消失，保持界面干净
    for i in tqdm(range(training_iter), desc="模型训练中", leave=False):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        #print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
    return model, likelihood

def scale_all_countries_data(result_dict, outlier_threshold=10.0, handle_outliers='cap'):
    """
    对所有国家的完整历史数据进行标准化处理。
    为每个国家创建一个scaler，并返回标准化后的数据字典和scaler字典。
    """
    result_dict_scale = {}
    scaler_dict = {} 

    for country, df in result_dict.items():
        scaler = StandardScaler()
        features = df.iloc[:, 2:].copy()
        
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR
                features.loc[features[col] < lower_bound, col] = lower_bound
                features.loc[features[col] > upper_bound, col] = upper_bound

        scaled_features = scaler.fit_transform(features)
        scaler_dict[country] = scaler
        
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale, scaler_dict

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, forecast_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', forecast_years)
    return pred_df

# ------------------------------------------------------------------------------
# 新增的核心预测函数
# ------------------------------------------------------------------------------

def predict_future_values(model, likelihood, train_len, pred_steps, embedding_vector, device):
    """使用训练好的模型进行未来值的预测"""
    # 创建未来的时间步
    future_time_steps = torch.arange(train_len, train_len + pred_steps, dtype=torch.float32).view(-1, 1).to(device)
    
    # 准备嵌入向量
    embedding_tensor = torch.tensor(embedding_vector, dtype=torch.float32).to(device)
    embedding_repeated = embedding_tensor.repeat(pred_steps, 1)
    
    # 组合成预测输入
    X_pred = torch.cat([future_time_steps, embedding_repeated], dim=1)
    
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        # 使用模型进行预测，并获取均值
        predictions = likelihood(model(X_pred)).mean.cpu().numpy()
    return predictions

def process_country_forecast(df_scaled, country_scaler, device, embedding_df, forecast_years):
    """
    处理单个国家的完整预测流程：训练 -> 预测 -> 还原尺度
    
    Args:
        df_scaled (pd.DataFrame): 单个国家的、已经标准化后的历史数据 (1960-2024).
        country_scaler (StandardScaler): 用于该国数据的scaler对象.
        device (torch.device): 'cuda' or 'cpu'.
        embedding_df (pd.DataFrame): 包含所有国家空间嵌入的DataFrame.
        forecast_years (list): 需要预测的年份列表, e.g., [2025, 2026, ..., 2100].

    Returns:
        pd.DataFrame: 包含该国未来预测值的DataFrame (原始数据尺度).
    """
    country = df_scaled['location'].iloc[0]
    
    # 1. 准备训练数据 (使用全部历史数据)
    features_scaled = df_scaled.iloc[:, 2:]
    
    # 获取国家嵌入向量 (32 维)
    embedding_vector = embedding_df.loc[country].values.reshape(1, -1)  # [1, 32]
    
    # 准备训练输入 X_train: 时间 + 嵌入向量
    time_steps = np.arange(len(features_scaled)).reshape(-1, 1)  # [num_years, 1]
    embedding_repeated = np.repeat(embedding_vector, len(features_scaled), axis=0)  # [num_years, 32]
    X_train = np.hstack([time_steps, embedding_repeated])  # [num_years, 33]
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    
    # 准备训练输出 y_train
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 2. 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 3. 预测未来值
    train_len = len(df_scaled)
    pred_steps = len(forecast_years)
    predictions_scaled = predict_future_values(model, likelihood, train_len, pred_steps, embedding_vector, device)

    # 4. 将预测结果还原到原始数据尺度
    predictions_raw = country_scaler.inverse_transform(predictions_scaled)

    # 5. 清理内存
    cleanup_memory()

    # 6. 构建并返回结果DataFrame
    pred_df_raw = build_prediction_df(predictions_raw, country, forecast_years, features_scaled.columns)
    
    return pred_df_raw

# ==============================================================================
# 主流程 (执行预测任务)
# ==============================================================================

if __name__ == '__main__':
    # 1. 加载数据
    print("正在加载数据...")
    try:
        filled_df = pd.read_csv("筛选后协变量特征值列表.csv")
        cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=0)
    except FileNotFoundError as e:
        print(f"错误: 无法找到文件 {e.filename}。请确保文件与脚本在同一目录下。")
        exit()
        
    print(f"原始数据加载完成，形状为: {filled_df.shape}")
    print(f"空间嵌入数据加载完成，形状为: {cor_space_country.shape}")

    # 2. 按国家分组
    grouped = filled_df.groupby('location')
    result_dict = {location: group.reset_index(drop=True) for location, group in grouped}

    # 3. 设置参数
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"将使用设备: {device}")
    
    # 定义预测年份范围
    forecast_start_year = 2025
    forecast_end_year = 2100
    forecast_years = list(range(forecast_start_year, forecast_end_year + 1))
    print(f"预测范围: {forecast_start_year} 年到 {forecast_end_year} 年，共 {len(forecast_years)} 年。")

    # 4. 对所有国家的历史数据进行标准化，并获取各自的scaler
    print("正在对所有国家的历史数据进行标准化...")
    result_dict_scaled, scaler_dict = scale_all_countries_data(result_dict)
    print("数据标准化完成。")

    # 5. 遍历所有国家，执行预测
    all_predictions_list = []
    countries_to_process = list(result_dict_scaled.keys())

    # 使用tqdm创建主进度条
    for country in tqdm(countries_to_process, desc="国家总预测进度"):
        df_scaled_country = result_dict_scaled[country]
        country_scaler = scaler_dict[country]
        
        # 执行单个国家的完整预测流程
        country_prediction_df = process_country_forecast(
            df_scaled=df_scaled_country,
            country_scaler=country_scaler,
            device=device,
            embedding_df=cor_space_country,
            forecast_years=forecast_years
        )
        
        all_predictions_list.append(country_prediction_df)

    # 6. 合并所有国家的预测结果并保存
    print("所有国家预测完成，正在合并结果...")
    final_predictions_df = pd.concat(all_predictions_list, ignore_index=True)

    # 7. 保存到CSV文件
    output_filename = "未来协变量预测结果(2025-2100).csv"
    final_predictions_df.to_csv(output_filename, index=False)

    print("-" * 50)
    print("预测流程全部完成！")
    print(f"最终预测结果的形状: {final_predictions_df.shape}")
    print(f"结果已保存至: {output_filename}")
    print("\n部分预测结果预览:")
    print(final_predictions_df.head())
